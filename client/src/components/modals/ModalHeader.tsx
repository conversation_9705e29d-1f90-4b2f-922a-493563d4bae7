/**
 * Reusable Modal Header Component
 * Adobe CEP-style header with 40px height, proper typography, and SVG close icon
 * Drop-in component for consistent modal headers across all modals
 */

import React from 'react';

interface ModalHeaderProps {
  title: string;
  onClose: () => void;
  onBack?: () => void;
  showBackButton?: boolean;
  children?: React.ReactNode; // For additional header content like search bars
}

export const ModalHeader: React.FC<ModalHeaderProps> = ({ 
  title, 
  onClose, 
  onBack, 
  showBackButton = false,
  children 
}) => {
  return (
    <header className="modal-header">
      <div className="modal-header-left">
        {showBackButton && onBack && (
          <button
            className="modal-back"
            aria-label="Go back"
            onClick={onBack}
            type="button"
          >
            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
            </svg>
          </button>
        )}
        <h2 className="modal-title">{title}</h2>
      </div>
      
      {children && (
        <div className="modal-header-center">
          {children}
        </div>
      )}
      
      <div className="modal-header-right">
        <button
          className="modal-close"
          aria-label="Close"
          onClick={onClose}
          type="button"
        >
          <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"/>
          </svg>
        </button>
      </div>
    </header>
  );
};

// Export default for easier imports
export default ModalHeader;
