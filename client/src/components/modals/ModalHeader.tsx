/**
 * Reusable Modal Header Component
 * Adobe CEP-style header with 40px height, proper typography, and SVG close icon
 * Drop-in component for consistent modal headers across all modals
 */

import React from 'react';

interface ModalHeaderProps {
  title: string;
  onClose: () => void;
  onBack?: () => void;
  showBackButton?: boolean;
  children?: React.ReactNode; // For additional header content like search bars
}

export const ModalHeader: React.FC<ModalHeaderProps> = ({ 
  title, 
  onClose, 
  onBack, 
  showBackButton = false,
  children 
}) => {
  return (
    <header className="modal-header">
      <div className="modal-header-left">
        {showBackButton && onBack && (
          <button
            className="modal-back"
            aria-label="Go back"
            onClick={onBack}
            type="button"
          >
            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M8 0a8 8 0 1 0 0 16A8 8 0 0 0 8 0zM5.354 4.646L4.646 5.354 7.293 8l-2.647 2.646.708.708L8 8.707l2.646 2.647.708-.708L8.707 8l2.647-2.646-.708-.708L8 7.293 5.354 4.646z"/>
            </svg>
          </button>
        )}
        <h2 className="modal-title">{title}</h2>
      </div>
      
      {children && (
        <div className="modal-header-center">
          {children}
        </div>
      )}
      
      <div className="modal-header-right">
        <button
          className="modal-close"
          aria-label="Close"
          onClick={onClose}
          type="button"
        >
          <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"/>
          </svg>
        </button>
      </div>
    </header>
  );
};

// Export default for easier imports
export default ModalHeader;
