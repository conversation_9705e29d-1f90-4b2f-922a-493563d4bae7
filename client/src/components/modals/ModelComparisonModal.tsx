/**
 * Model Comparison Modal for V2 - Adobe CEP Style
 * Allows users to compare different AI models side by side
 * Updated with Modal<PERSON>eader component and Adobe styling
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { ModalHeader } from './ModalHeader';
import { CompareIcon, StarIcon, ClockIcon, DollarSignIcon } from '../ui';
import styles from './ModalContent.module.css';

interface ModelComparisonModalProps {
  onClose: () => void;
  onBack: () => void;
}

interface ModelMetrics {
  modelId: string;
  modelName: string;
  providerName: string;
  speed: number; // tokens per second
  cost: number; // cost per 1k tokens
  quality: number; // subjective rating 1-5
  contextLength: number;
  capabilities: string[];
  strengths: string[];
  weaknesses: string[];
  bestFor: string[];
}

const MOCK_MODEL_METRICS: ModelMetrics[] = [
  {
    modelId: 'claude-3-5-sonnet-20241022',
    modelName: 'Claude 3.5 Sonnet',
    providerName: 'Anthropic',
    speed: 45,
    cost: 0.003,
    quality: 5,
    contextLength: 200000,
    capabilities: ['Text', 'Code', 'Analysis', 'Creative Writing'],
    strengths: ['Excellent reasoning', 'Code generation', 'Long context'],
    weaknesses: ['Higher cost', 'No image generation'],
    bestFor: ['Complex analysis', 'Code review', 'Research']
  },
  {
    modelId: 'gpt-4-turbo',
    modelName: 'GPT-4 Turbo',
    providerName: 'OpenAI',
    speed: 35,
    cost: 0.01,
    quality: 5,
    contextLength: 128000,
    capabilities: ['Text', 'Code', 'Vision', 'Function Calling'],
    strengths: ['Versatile', 'Function calling', 'Vision capabilities'],
    weaknesses: ['Expensive', 'Can be verbose'],
    bestFor: ['General tasks', 'API integration', 'Multimodal tasks']
  },
  {
    modelId: 'gemini-pro',
    modelName: 'Gemini Pro',
    providerName: 'Google',
    speed: 50,
    cost: 0.0005,
    quality: 4,
    contextLength: 32000,
    capabilities: ['Text', 'Code', 'Vision', 'Multimodal'],
    strengths: ['Fast', 'Cost-effective', 'Good at factual queries'],
    weaknesses: ['Shorter context', 'Less creative'],
    bestFor: ['Quick queries', 'Factual information', 'Budget-conscious use']
  },
  {
    modelId: 'llama-2-70b',
    modelName: 'Llama 2 70B',
    providerName: 'Meta',
    speed: 25,
    cost: 0.0007,
    quality: 4,
    contextLength: 4096,
    capabilities: ['Text', 'Code', 'Reasoning'],
    strengths: ['Open source', 'Good reasoning', 'Privacy-focused'],
    weaknesses: ['Limited context', 'Slower', 'Requires local setup'],
    bestFor: ['Privacy-sensitive tasks', 'Local deployment', 'Open source projects']
  }
];

export const ModelComparisonModal: React.FC<ModelComparisonModalProps> = ({
  onClose,
  onBack
}) => {
  const { providers, models } = useSettingsStore();
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [comparisonData, setComparisonData] = useState<ModelMetrics[]>([]);
  const [sortBy, setSortBy] = useState<'speed' | 'cost' | 'quality' | 'context'>('quality');

  useEffect(() => {
    // In a real implementation, this would fetch actual metrics
    setComparisonData(MOCK_MODEL_METRICS);

    // Auto-select first 2 models for comparison
    if (MOCK_MODEL_METRICS.length >= 2) {
      setSelectedModels([MOCK_MODEL_METRICS[0]?.modelId || '', MOCK_MODEL_METRICS[1]?.modelId || ''].filter(Boolean));
    }
  }, []);

  const handleModelToggle = (modelId: string) => {
    setSelectedModels(prev => {
      if (prev.includes(modelId)) {
        return prev.filter(id => id !== modelId);
      } else if (prev.length < 4) { // Limit to 4 models for comparison
        return [...prev, modelId];
      }
      return prev;
    });
  };

  const getSelectedModelData = () => {
    return comparisonData.filter(model => selectedModels.includes(model.modelId));
  };

  const renderModelCard = (model: ModelMetrics, isSelected: boolean) => (
    <div
      key={model.modelId}
      className={`${styles['model-card']} ${isSelected ? styles['selected'] : ''}`}
      onClick={() => handleModelToggle(model.modelId)}
    >
      <div className={styles['model-header']}>
        <h4 className={styles['model-name']}>{model.modelName}</h4>
        <span className={styles['provider-name']}>{model.providerName}</span>
        <div className={styles['quality-rating']}>
          {Array.from({ length: 5 }, (_, i) => (
            <StarIcon
              key={i}
              size={12}
              className={i < model.quality ? styles['star-filled'] : styles['star-empty']}
            />
          ))}
        </div>
      </div>
      
      <div className={styles['model-metrics']}>
        <div className={styles['metric']}>
          <ClockIcon size={14} />
          <span>{model.speed} tok/s</span>
        </div>
        <div className={styles['metric']}>
          <DollarSignIcon size={14} />
          <span>${model.cost}/1k</span>
        </div>
        <div className={styles['metric']}>
          <span className={styles['context-length']}>
            {model.contextLength.toLocaleString()} ctx
          </span>
        </div>
      </div>
      
      <div className={styles['model-capabilities']}>
        {model.capabilities.slice(0, 3).map(cap => (
          <span key={cap} className={styles['capability-tag']}>{cap}</span>
        ))}
        {model.capabilities.length > 3 && (
          <span className={styles['capability-more']}>+{model.capabilities.length - 3}</span>
        )}
      </div>
    </div>
  );

  const renderComparisonTable = () => {
    const selectedData = getSelectedModelData();
    
    if (selectedData.length === 0) {
      return (
        <div className={styles['no-selection']}>
          <CompareIcon size={48} className={styles['no-selection-icon'] || ''} />
          <p>Select models to compare them side by side</p>
        </div>
      );
    }

    return (
      <div className={styles['comparison-table']}>
        <table className={styles['table']}>
          <thead>
            <tr>
              <th>Metric</th>
              {selectedData.map(model => (
                <th key={model.modelId}>{model.modelName}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><strong>Provider</strong></td>
              {selectedData.map(model => (
                <td key={model.modelId}>{model.providerName}</td>
              ))}
            </tr>
            <tr>
              <td><strong>Speed (tokens/sec)</strong></td>
              {selectedData.map(model => (
                <td key={model.modelId}>{model.speed}</td>
              ))}
            </tr>
            <tr>
              <td><strong>Cost (per 1k tokens)</strong></td>
              {selectedData.map(model => (
                <td key={model.modelId}>${model.cost}</td>
              ))}
            </tr>
            <tr>
              <td><strong>Quality Rating</strong></td>
              {selectedData.map(model => (
                <td key={model.modelId}>
                  <div className={styles['rating-display']}>
                    {Array.from({ length: 5 }, (_, i) => (
                      <StarIcon
                        key={i}
                        size={12}
                        className={i < model.quality ? styles['star-filled'] : styles['star-empty']}
                      />
                    ))}
                  </div>
                </td>
              ))}
            </tr>
            <tr>
              <td><strong>Context Length</strong></td>
              {selectedData.map(model => (
                <td key={model.modelId}>{model.contextLength.toLocaleString()}</td>
              ))}
            </tr>
            <tr>
              <td><strong>Capabilities</strong></td>
              {selectedData.map(model => (
                <td key={model.modelId}>
                  <div className={styles['capabilities-list']}>
                    {model.capabilities.map(cap => (
                      <span key={cap} className={styles['capability-tag-small']}>{cap}</span>
                    ))}
                  </div>
                </td>
              ))}
            </tr>
            <tr>
              <td><strong>Strengths</strong></td>
              {selectedData.map(model => (
                <td key={model.modelId}>
                  <ul className={styles['feature-list']}>
                    {model.strengths.map(strength => (
                      <li key={strength}>{strength}</li>
                    ))}
                  </ul>
                </td>
              ))}
            </tr>
            <tr>
              <td><strong>Best For</strong></td>
              {selectedData.map(model => (
                <td key={model.modelId}>
                  <ul className={styles['feature-list']}>
                    {model.bestFor.map(use => (
                      <li key={use}>{use}</li>
                    ))}
                  </ul>
                </td>
              ))}
            </tr>
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className={styles['modal-content']}>
      <ModalHeader
        title="Model Comparison"
        onClose={onClose}
        onBack={onBack}
        showBackButton={true}
      >
        {/* Sort control in header */}
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as any)}
          className={`${styles['adobe-input']} ${styles['header-sort']}`}
        >
          <option value="quality">Sort by Quality</option>
          <option value="speed">Sort by Speed</option>
          <option value="cost">Sort by Cost</option>
          <option value="context">Sort by Context Length</option>
        </select>
      </ModalHeader>

      <div className={styles['modal-body']}>
        {/* Model Selection */}
        <div className={styles['model-selection']}>
          <h3 className={styles['section-header']}>
            Select Models to Compare ({selectedModels.length}/4)
          </h3>
          <div className={styles['model-grid']}>
            {comparisonData
              .sort((a, b) => {
                switch (sortBy) {
                  case 'speed': return b.speed - a.speed;
                  case 'cost': return a.cost - b.cost;
                  case 'context': return b.contextLength - a.contextLength;
                  default: return b.quality - a.quality;
                }
              })
              .map(model => renderModelCard(model, selectedModels.includes(model.modelId)))}
          </div>
        </div>

        {/* Comparison Results */}
        <div className={styles['comparison-results']}>
          <h3 className={styles['section-header']}>Comparison Results</h3>
          {renderComparisonTable()}
        </div>

        {/* Recommendations */}
        {selectedModels.length > 0 && (
          <div className={styles['recommendations']}>
            <h4 className={styles['subsection-header']}>Recommendations</h4>
            <div className={styles['recommendation-cards']}>
              <div className={styles['recommendation-card']}>
                <h5>💰 Most Cost-Effective</h5>
                <p>
                  {getSelectedModelData().reduce((min, model) => 
                    model.cost < min.cost ? model : min
                  ).modelName}
                </p>
              </div>
              <div className={styles['recommendation-card']}>
                <h5>⚡ Fastest</h5>
                <p>
                  {getSelectedModelData().reduce((max, model) => 
                    model.speed > max.speed ? model : max
                  ).modelName}
                </p>
              </div>
              <div className={styles['recommendation-card']}>
                <h5>🏆 Highest Quality</h5>
                <p>
                  {getSelectedModelData().reduce((max, model) => 
                    model.quality > max.quality ? model : max
                  ).modelName}
                </p>
              </div>
              <div className={styles['recommendation-card']}>
                <h5>📚 Largest Context</h5>
                <p>
                  {getSelectedModelData().reduce((max, model) => 
                    model.contextLength > max.contextLength ? model : max
                  ).modelName}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModelComparisonModal;
