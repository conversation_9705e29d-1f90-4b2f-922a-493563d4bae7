/**
 * About Modal for V2 - Adobe CEP Style
 * Displays application information, version, features, and useful links
 * Updated with ModalHeader component and SVG icons
 */

import React from 'react';
import { ModalHeader } from './ModalHeader';
import styles from './ModalContent.module.css';

interface AboutModalProps {
  onClose: () => void;
  onBack: () => void;
}

export const AboutModal: React.FC<AboutModalProps> = ({ onClose, onBack }) => {
  const handleLinkClick = (linkType: string) => {
    console.log(`Opening: ${linkType}`);
    // In a real implementation, these would open actual links
    switch (linkType) {
      case 'Documentation':
        // window.open('https://docs.sahai.ai', '_blank');
        break;
      case 'GitHub Repository':
        // window.open('https://github.com/sahai-ai/cep-extension', '_blank');
        break;
      case 'Privacy Policy':
        // window.open('https://sahai.ai/privacy', '_blank');
        break;
      case 'Terms of Service':
        // window.open('https://sahai.ai/terms', '_blank');
        break;
      default:
        break;
    }
  };

  return (
    <div className={styles['modal-content']}>
      <ModalHeader
        title="About SahAI Extension"
        onClose={onClose}
      />

      <div className={styles['modal-body']}>
        <div className={styles['about-section']}>
          {/* Logo Section */}
          <div className={styles['logo-section']}>
            <div className={styles['app-icon']}>
              <svg width="32" height="32" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                <path d="M9.05 4.05a3 3 0 1 0-2.1 0L5.5 5.5a.5.5 0 0 0 0 .707L7.146 7.854a.5.5 0 0 0 .708 0L9.5 6.207a.5.5 0 0 0 0-.707L9.05 4.05zM8 5.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z"/>
              </svg>
            </div>
            <h2 className={styles['app-name']}>SahAI Extension</h2>
            <p className={styles['version']}>Version 2.0.0</p>
          </div>

          {/* Info Section */}
          <div className={styles['info-section']}>
            <h3 className={styles['section-header']}>About This Extension</h3>
            <p className={styles['section-description']}>
              SahAI is a powerful AI extension designed to enhance your creative workflow.
              It integrates seamlessly with Adobe Creative Suite to provide intelligent assistance
              for your creative projects.
            </p>

            {/* Features List */}
            <ul className={styles['features-list']}>
              <li className={styles['feature-item']}>
                <span className={styles['feature-icon']}>
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/>
                  </svg>
                </span>
                <div>
                  <strong>Multi-Model Support:</strong> Access various AI models including GPT-4, Claude, and more.
                </div>
              </li>
              <li className={styles['feature-item']}>
                <span className={styles['feature-icon']}>
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M1.5 1.5A.5.5 0 0 1 2 1h12a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.128.334L10 8.692V13.5a.5.5 0 0 1-.342.474l-3 1A.5.5 0 0 1 6 14.5V8.692L1.628 3.834A.5.5 0 0 1 1.5 3.5v-2z"/>
                  </svg>
                </span>
                <div>
                  <strong>Usage Analytics:</strong> Track your AI interactions and performance metrics.
                </div>
              </li>
              <li className={styles['feature-item']}>
                <span className={styles['feature-icon']}>
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M9.405 1.05c-.413-1.4-2.397-1.4-2.81 0l-.1.34a1.464 1.464 0 0 1-2.105.872l-.31-.17c-1.283-.698-2.686.705-1.987 1.987l.169.311c.446.82.023 1.841-.872 2.105l-.34.1c-1.4.413-1.4 2.397 0 2.81l.34.1a1.464 1.464 0 0 1 .872 2.105l-.17.31c-.698 1.283.705 2.686 1.987 1.987l.311-.169a1.464 1.464 0 0 1 2.105.872l.1.34c.413 1.4 2.397 1.4 2.81 0l.1-.34a1.464 1.464 0 0 1 2.105-.872l.31.17c1.283.698 2.686-.705 1.987-1.987l-.169-.311a1.464 1.464 0 0 1 .872-2.105l.34-.1c1.4-.413 1.4-2.397 0-2.81l-.34-.1a1.464 1.464 0 0 1-.872-2.105l.17-.31c.698-1.283-.705-2.686-1.987-1.987l-.311.169a1.464 1.464 0 0 1-2.105-.872l-.1-.34zM8 10.93a2.929 2.929 0 1 1 0-5.86 2.929 2.929 0 0 1 0 5.858z"/>
                  </svg>
                </span>
                <div>
                  <strong>Advanced Configuration:</strong> Tailor settings to your specific needs.
                </div>
              </li>
              <li className={styles['feature-item']}>
                <span className={styles['feature-icon']}>
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M1 2.828c.885-.37 2.154-.769 3.388-.893 1.33-.134 2.458.063 3.112.752v9.746c-.935-.53-2.12-.603-3.213-.493-1.18.12-2.37.461-3.287.811V2.828zm7.5-.141c.654-.689 1.782-.886 3.112-.752 1.234.124 2.503.523 3.388.893v9.923c-.918-.35-2.107-.692-3.287-.81-1.094-.111-2.278-.039-3.213.492V2.687zM8 1.783C7.015.936 5.587.81 4.287.94c-1.514.153-3.042.672-3.994 1.105A.5.5 0 0 0 0 2.5v11a.5.5 0 0 0 .707.455c.882-.4 2.303-.881 3.68-1.02 1.409-.142 2.59.087 3.223.877a.5.5 0 0 0 .78 0c.633-.79 1.814-1.019 3.222-.877 1.378.139 2.8.62 3.681 1.02A.5.5 0 0 0 16 13.5v-11a.5.5 0 0 0-.293-.455c-.952-.433-2.48-.952-3.994-1.105C10.413.809 8.985.936 8 1.783z"/>
                  </svg>
                </span>
                <div>
                  <strong>Comprehensive Help:</strong> Get started quickly with built-in guidance.
                </div>
              </li>
              <li className={styles['feature-item']}>
                <span className={styles['feature-icon']}>
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
                  </svg>
                </span>
                <div>
                  <strong>Secure & Private:</strong> Your data stays secure with local encryption.
                </div>
              </li>
              <li className={styles['feature-item']}>
                <span className={styles['feature-icon']}>
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M2 1a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h9.586a2 2 0 0 1 1.414.586l2 2V2a1 1 0 0 0-1-1H2zm12-1a2 2 0 0 1 2 2v12.793a.5.5 0 0 1-.854.353l-2.853-2.853a1 1 0 0 0-.707-.293H2a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h12z"/>
                    <path d="M5 6a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm4 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm4 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/>
                  </svg>
                </span>
                <div>
                  <strong>Creative Suite Integration:</strong> Works seamlessly with Adobe applications.
                </div>
              </li>
            </ul>
          </div>

          {/* Links Section */}
          <div className={styles['links-section']}>
            <h3 className={styles['section-header']}>Useful Links</h3>
            <div className={styles['link-grid']}>
              <button
                className={`${styles['link-button']} ${styles['adobe-btn']} ${styles['secondary']}`}
                onClick={() => handleLinkClick('Documentation')}
              >
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M1 2.828c.885-.37 2.154-.769 3.388-.893 1.33-.134 2.458.063 3.112.752v9.746c-.935-.53-2.12-.603-3.213-.493-1.18.12-2.37.461-3.287.811V2.828zm7.5-.141c.654-.689 1.782-.886 3.112-.752 1.234.124 2.503.523 3.388.893v9.923c-.918-.35-2.107-.692-3.287-.81-1.094-.111-2.278-.039-3.213.492V2.687zM8 1.783C7.015.936 5.587.81 4.287.94c-1.514.153-3.042.672-3.994 1.105A.5.5 0 0 0 0 2.5v11a.5.5 0 0 0 .707.455c.882-.4 2.303-.881 3.68-1.02 1.409-.142 2.59.087 3.223.877a.5.5 0 0 0 .78 0c.633-.79 1.814-1.019 3.222-.877 1.378.139 2.8.62 3.681 1.02A.5.5 0 0 0 16 13.5v-11a.5.5 0 0 0-.293-.455c-.952-.433-2.48-.952-3.994-1.105C10.413.809 8.985.936 8 1.783z"/>
                </svg>
                Documentation
              </button>
              <button
                className={`${styles['link-button']} ${styles['adobe-btn']} ${styles['secondary']}`}
                onClick={() => handleLinkClick('GitHub Repository')}
              >
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8z"/>
                </svg>
                GitHub Repository
              </button>
              <button
                className={`${styles['link-button']} ${styles['adobe-btn']} ${styles['secondary']}`}
                onClick={() => handleLinkClick('Privacy Policy')}
              >
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
                </svg>
                Privacy Policy
              </button>
              <button
                className={`${styles['link-button']} ${styles['adobe-btn']} ${styles['secondary']}`}
                onClick={() => handleLinkClick('Terms of Service')}
              >
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2zM9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5v2z"/>
                </svg>
                Terms of Service
              </button>
            </div>
          </div>

          {/* Copyright Section */}
          <div className={styles['copyright-section']}>
            <p className={styles['copyright']}>© 2025 SahAI Extension. All rights reserved.</p>
            <p className={styles['disclaimer']}>
              This software is provided "as is," without warranty of any kind.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutModal;
