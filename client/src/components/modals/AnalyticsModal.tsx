/**
 * Analytics Modal for V2 - Adobe CEP Style
 * Displays usage statistics and performance metrics for AI models
 * Updated with ModalHeader component and Adobe styling
 */

import React, { useState, useEffect } from 'react';
import { useChatStore } from '../../stores/chatStore';
import { useSettingsStore } from '../../stores/settingsStore';
import { ModalHeader } from './ModalHeader';
import { RefreshIcon, TrendingUpIcon, MessageSquareIcon, ClockIcon } from '../ui';
import styles from './ModalContent.module.css';

interface AnalyticsModalProps {
  onClose: () => void;
  onBack: () => void;
}

interface UsageStats {
  totalMessages: number;
  totalSessions: number;
  averageResponseTime: number;
  mostUsedProvider: string;
  mostUsedModel: string;
  tokensUsed: number;
  estimatedCost: number;
}

interface ProviderUsage {
  providerId: string;
  providerName: string;
  messageCount: number;
  averageResponseTime: number;
  successRate: number;
}

export const AnalyticsModal: React.FC<AnalyticsModalProps> = ({
  onClose,
  onBack
}) => {
  const { sessions } = useChatStore();
  const { providers } = useSettingsStore();
  const [isLoading, setIsLoading] = useState(false);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');

  // Calculate usage statistics
  const calculateStats = (): UsageStats => {
    const totalMessages = sessions.reduce((sum, session) => sum + session.messages.length, 0);
    const totalSessions = sessions.length;
    
    // Real analytics calculations - these would be calculated from actual usage data
    const averageResponseTime = 0; // TODO: Calculate from actual response times when available
    const tokensUsed = 0; // TODO: Calculate from actual token usage when available
    const estimatedCost = 0; // TODO: Calculate from actual API costs when available
    
    // Find most used provider/model from session metadata
    const providerCounts: Record<string, number> = {};
    const modelCounts: Record<string, number> = {};
    
    sessions.forEach(session => {
      if (session.metadata?.provider) {
        providerCounts[session.metadata.provider] = (providerCounts[session.metadata.provider] || 0) + 1;
      }
      if (session.metadata?.model) {
        modelCounts[session.metadata.model] = (modelCounts[session.metadata.model] || 0) + 1;
      }
    });
    
    const mostUsedProvider = Object.keys(providerCounts).length > 0
      ? Object.keys(providerCounts).reduce((a, b) =>
          (providerCounts[a] || 0) > (providerCounts[b] || 0) ? a : b
        )
      : 'N/A';

    const mostUsedModel = Object.keys(modelCounts).length > 0
      ? Object.keys(modelCounts).reduce((a, b) =>
          (modelCounts[a] || 0) > (modelCounts[b] || 0) ? a : b
        )
      : 'N/A';

    return {
      totalMessages,
      totalSessions,
      averageResponseTime,
      mostUsedProvider,
      mostUsedModel,
      tokensUsed,
      estimatedCost
    };
  };

  // Calculate provider usage breakdown
  const calculateProviderUsage = (): ProviderUsage[] => {
    const providerStats: Record<string, { messages: number; sessions: number }> = {};
    
    sessions.forEach(session => {
      const providerId = session.metadata?.provider || 'unknown';
      if (!providerStats[providerId]) {
        providerStats[providerId] = { messages: 0, sessions: 0 };
      }
      providerStats[providerId].messages += session.messages.length;
      providerStats[providerId].sessions += 1;
    });

    return Object.entries(providerStats).map(([providerId, stats]) => {
      const provider = providers.find(p => p.id === providerId);
      return {
        providerId,
        providerName: provider?.name || providerId,
        messageCount: stats.messages,
        averageResponseTime: 1000 + Math.random() * 1000, // Mock response time
        successRate: 95 + Math.random() * 5 // Mock success rate 95-100%
      };
    }).sort((a, b) => b.messageCount - a.messageCount);
  };

  const [stats, setStats] = useState<UsageStats>(calculateStats());
  const [providerUsage, setProviderUsage] = useState<ProviderUsage[]>(calculateProviderUsage());

  const handleRefresh = async () => {
    setIsLoading(true);
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    setStats(calculateStats());
    setProviderUsage(calculateProviderUsage());
    setIsLoading(false);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const formatTime = (ms: number) => {
    return `${(ms / 1000).toFixed(1)}s`;
  };

  return (
    <div className={styles['modal-content']}>
      <ModalHeader
        title="Analytics Dashboard"
        onClose={onClose}
        onBack={onBack}
        showBackButton={true}
      >
        {/* Time range and refresh controls in header */}
        <div className={styles['header-controls']}>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className={`${styles['adobe-input']} ${styles['header-sort']}`}
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="all">All time</option>
          </select>
          <button
            className={`${styles['adobe-btn']} ${styles['primary']}`}
            onClick={handleRefresh}
            disabled={isLoading}
            title="Refresh analytics"
          >
            <RefreshIcon size={16} className={isLoading ? (styles['spinning'] || '') : ''} />
            {isLoading ? 'Loading...' : 'Refresh'}
          </button>
        </div>
      </ModalHeader>

      <div className={styles['modal-body']}>
        {/* Overview Stats */}
        <div className={styles['overview-stats']}>
          <div className={styles['stat-item']}>
            <MessageSquareIcon size={20} className={styles['stat-icon'] || ''} />
            <div className={styles['stat-content']}>
              <span className={styles['stat-label']}>Total Messages</span>
              <span className={styles['stat-value']}>{formatNumber(stats.totalMessages)}</span>
            </div>
          </div>

          <div className={styles['stat-item']}>
            <TrendingUpIcon size={20} className={styles['stat-icon'] || ''} />
            <div className={styles['stat-content']}>
              <span className={styles['stat-label']}>Chat Sessions</span>
              <span className={styles['stat-value']}>{formatNumber(stats.totalSessions)}</span>
            </div>
          </div>

          <div className={styles['stat-item']}>
            <ClockIcon size={20} className={styles['stat-icon'] || ''} />
            <div className={styles['stat-content']}>
              <span className={styles['stat-label']}>Avg Response Time</span>
              <span className={styles['stat-value']}>{formatTime(stats.averageResponseTime)}</span>
            </div>
          </div>
          
          <div className={styles['stat-item']}>
            <span className={styles['stat-icon']}>💰</span>
            <div className={styles['stat-content']}>
              <span className={styles['stat-label']}>Estimated Cost</span>
              <span className={styles['stat-value']}>{formatCurrency(stats.estimatedCost)}</span>
            </div>
          </div>
        </div>

        {/* Usage Summary */}
        <div className={styles['usage-summary']}>
          <h3 className={styles['section-header']}>Usage Summary</h3>
          <div className={styles['summary-grid']}>
            <div className={styles['summary-item']}>
              <span className={styles['summary-label']}>Most Used Provider:</span>
              <span className={styles['summary-value']}>{stats.mostUsedProvider}</span>
            </div>
            <div className={styles['summary-item']}>
              <span className={styles['summary-label']}>Most Used Model:</span>
              <span className={styles['summary-value']}>{stats.mostUsedModel}</span>
            </div>
            <div className={styles['summary-item']}>
              <span className={styles['summary-label']}>Total Tokens:</span>
              <span className={styles['summary-value']}>{formatNumber(stats.tokensUsed)}</span>
            </div>
          </div>
        </div>

        {/* Provider Breakdown */}
        <div className={styles['provider-breakdown']}>
          <h3 className={styles['section-header']}>Provider Performance</h3>
          <div className={styles['provider-list']}>
            {providerUsage.map((provider) => (
              <div key={provider.providerId} className={styles['provider-item']}>
                <div className={styles['provider-header']}>
                  <h4 className={styles['provider-name']}>{provider.providerName}</h4>
                  <span className={styles['message-count']}>
                    {formatNumber(provider.messageCount)} messages
                  </span>
                </div>
                <div className={styles['provider-metrics']}>
                  <div className={styles['metric']}>
                    <span className={styles['metric-label']}>Avg Response:</span>
                    <span className={styles['metric-value']}>
                      {formatTime(provider.averageResponseTime)}
                    </span>
                  </div>
                  <div className={styles['metric']}>
                    <span className={styles['metric-label']}>Success Rate:</span>
                    <span className={styles['metric-value']}>
                      {provider.successRate.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Tips Section */}
        <div className={styles['tips-section']}>
          <h4 className={styles['subsection-header']}>Performance Tips</h4>
          <ul className={styles['tips-list']}>
            <li className={styles['tip-item']}>
              <strong>Optimize prompts:</strong> Shorter, clearer prompts often yield better results and lower costs.
            </li>
            <li className={styles['tip-item']}>
              <strong>Choose the right model:</strong> Use faster models for simple tasks and advanced models for complex reasoning.
            </li>
            <li className={styles['tip-item']}>
              <strong>Monitor usage:</strong> Keep track of your API usage to avoid unexpected costs.
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsModal;
