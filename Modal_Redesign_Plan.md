# 📋 Adobe CEP Modal Unification Plan  
*Professional, native-looking slide-in modals for **SahAI V2***

---

## 1. Design Targets  
| Adobe Element        | What we’ll adopt                               |
|----------------------|------------------------------------------------|
| **Menu bar**         | 40 px header, left-aligned title, right-aligned action icon |
| **Panel background** | `var(--adobe-bg-secondary)` (matches PS, AE panels) |
| **Border**           | 1 px solid `var(--adobe-border)`               |
| **Border-radius**    | **0**  (panels are rectangular)                |
| **Shadow**           | 2 px left drop-shadow only (`rgba(0,0,0,.35)`) |
| **Typography**       | 14 pt **Segoe UI** (Adobe default)             |
| **Accent**           | `var(--adobe-accent)` (#46a0f5)                |

---

## 2. Modal Inventory & Changes

| Modal File (`client/src/components/modals/…`) | Header | Body | Footer / Actions | Special Notes |
|---|---|---|---|---|
| **AboutModal.tsx** | Add 40 px bar with “About SahAI” + ✕ | Remove card-radius, use plain list | — | Replace emoji icons with 16 px monochrome SVG |
| **ChatHistoryModal.tsx** | Add header bar (title + close) | Keep list, remove rounded corners | Keep “Showing X of Y” footer | Move search & sort into header bar |
| **SettingsModal.tsx** | Already good (header + back) | No change | No change | — |
| **AdvancedConfigModal.tsx** | Add header bar | Flatten tab cards (no radius) | Add 40 px footer bar with **Save / Reset** buttons | Tabs → Adobe segmented control |
| **AnalyticsModal.tsx** | Add header bar | Remove rounded card | — | Stats in Adobe **Property Inspector** style |
| **ModelComparisonModal.tsx** | Add header bar | Grid → plain list, no radius | Keep recommendations | Replace star emoji with 12 px star SVG |
| **MultiModelModal.tsx** | Add header bar | Remove rounded cards | — | Replace chip pills with Adobe **toggle buttons** |
| **ProviderHealthModal.tsx** | Add header bar | Remove rounded cards | — | Status list → Adobe **list view** |
| **HelpModal.tsx** | Add header bar | Remove rounded cards | — | FAQ → Adobe **accordion** (no radius) |

---

## 3. Adobe-Style Header Snippet (drop-in)

```tsx
// client/src/components/modals/ModalHeader.tsx
export const ModalHeader = ({ title, onClose }) => (
  <header className="modal-header">
    <h2 className="modal-title">{title}</h2>
    <button
      className="modal-close"
      aria-label="Close"
      onClick={onClose}
      type="button"
    >
      <svg width="16" height="16" fill="currentColor">
        <path d="M2 2L14 14M14 2L2 14" strokeWidth="1.5" />
      </svg>
    </button>
  </header>
);
```

CSS (add to `ModalContent.module.css`):

```css
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 0 12px;
  background: var(--adobe-bg-secondary);
  border-bottom: 1px solid var(--adobe-border);
  font-family: var(--adobe-font-family);
}

.modal-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: var(--adobe-text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  transition: 0.15s;
}

.modal-close:hover {
  background: var(--adobe-bg-tertiary);
  color: var(--adobe-text-primary);
}
```

---

## 4. Accent Button (Adobe style)

```tsx
<button className="adobe-btn primary">Save</button>
```

CSS:

```css
.adobe-btn.primary {
  height: 24px;
  padding: 0 12px;
  border: none;
  border-radius: 2px;
  background: var(--adobe-accent);
  color: #fff;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.15s;
}

.adobe-btn.primary:hover {
  background: var(--adobe-accent-hover);
}
```

---

## 5. Specific Quick Fixes

| Location | Before | After |
|---|---|---|
| `ChatHistoryModal.tsx` search input | `<input className={styles.searchInput} />` | Move into header; replace class with `adobe-input`. |
| `ModelComparisonModal.tsx` cards | `border-radius: 8px` | **remove** radius. |
| `Accordion` (FAQ) | custom radius | **remove** radius; use border-top only. |
| `ModalContent.module.css` scrollbars | custom | **reuse** `.modal-body::-webkit-scrollbar` style. |

---

## 6. Final Checklist

- [ ] Replace **all rounded corners** (`border-radius: 8px`, `12px`) with **0 px** except small 2 px on buttons.  
- [ ] Convert **emoji icons** → 16 px monochrome SVG.  
- [ ] Ensure every modal has **ModalHeader** (40 px) + **ModalBody** + optional **ModalFooter**.  
- [ ] Apply **Adobe accent color** to primary actions only.  
- [ ] Use **Segoe UI, 12–14 px** consistently.  
- [ ] Add **left-only drop-shadow** to slide-in container (`box-shadow: -2px 0 6px rgba(0,0,0,.35)`).  

> ✅ After these changes every modal will feel like a native Adobe panel—clean, rectangular, high-contrast, and keyboard-friendly.